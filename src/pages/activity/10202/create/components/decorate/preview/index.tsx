import PhonePreview from '@/components/PhonePreview/PhonePreview';
import styles from './index.module.scss';
import { useEffect, useState, useMemo, useRef } from 'react';
import { createTurnTableMainStyles, scrollToTop } from './utils/styleUtils';

// 奖品滑动组件
function PrizeSwiper({ prizes, tankNum, step2Bg }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const swiperRef = useRef<HTMLDivElement>(null);

  // 过滤出上架的奖品
  const activePrizes = useMemo(() => {
    return (prizes || []).filter(prize => prize.status === 1 || prize.status === undefined);
  }, [prizes]);

  // 滑动检测
  const handleScroll = (e) => {
    const container = e.target;
    const itemWidth = 240; // 卡片宽度
    const gap = 15; // 卡片之间的间距
    const { scrollLeft } = container;

    // 计算当前索引
    const newIndex = Math.round(scrollLeft / (itemWidth + gap));
    const validIndex = Math.max(0, Math.min(newIndex, activePrizes.length - 1));

    if (validIndex !== currentIndex) {
      setCurrentIndex(validIndex);
    }
  };

  // 滚动到指定索引
  const scrollToIndex = (index) => {
    if (swiperRef.current) {
      const itemWidth = 240;
      const gap = 15;
      const scrollLeft = index * (itemWidth + gap);
      swiperRef.current.scrollTo({
        left: scrollLeft,
        behavior: 'smooth',
      });
    }
  };

  // 鼠标拖拽滑动功能
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollStart, setScrollStart] = useState(0);

  const handleMouseDown = (e) => {
    if (!swiperRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - swiperRef.current.offsetLeft);
    setScrollStart(swiperRef.current.scrollLeft);
    swiperRef.current.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !swiperRef.current) return;
    e.preventDefault();
    const x = e.pageX - swiperRef.current.offsetLeft;
    const walk = (x - startX) * 2; // 滑动速度倍数
    swiperRef.current.scrollLeft = scrollStart - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (swiperRef.current) {
      swiperRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    if (swiperRef.current) {
      swiperRef.current.style.cursor = 'grab';
    }
  };

  if (!activePrizes || activePrizes.length === 0) {
    return (
      <div className={styles.step2Bg}>
        <div className={styles.prizeHeader}>{tankNum}罐可兑</div>
        <div className={styles.noPrize}>暂无奖品</div>
      </div>
    );
  }

  return (
    <div className={styles.step2Bg}>
      <div className={styles.swiperBigBox}>
        <div className={styles.swiperContent}>
          <div
            className={styles.swiperContainer}
            ref={swiperRef}
            onScroll={handleScroll}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            style={{ cursor: 'grab' }}
          >
            <div className={styles.swiperWrapper}>
              {activePrizes.map((prize, index) => (
                <div
                  key={index}
                  className={`${styles.swiperSlide} ${index === currentIndex ? styles.swiperSlideActive : ''}`}
                  style={{
                    backgroundImage: `url(${step2Bg})`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '100% 100%',
                    width: '240px',
                    height: '180px',
                  }}
                >
                  <div className={styles.prizeTitle}>
                    {tankNum}罐可兑
                  </div>

                  <div className={styles.prizeGoods}>
                    <div className={styles.prizeGoodsImgBox}>
                      <img
                        className={styles.prizeGoodsImg}
                        src={prize.showImage || prize.prizeImg || prize.image || ''}
                        alt={prize.lotteryName || prize.prizeName || prize.name || '奖品'}
                      />
                    </div>
                    <div className={styles.prizeGoodsInfo}>
                      <div className={styles.prizeGoodsName}>
                        {prize.lotteryName || prize.prizeName || prize.name || '奖品名称'}
                      </div>
                      <div className={styles.step2ItemBg}>
                        立即兑换
                      </div>
                      <div className={styles.prizeGoodsRemain}>
                        奖品剩余：{prize.prizeNum || prize.stock || prize.remainCount || 0}份
                      </div>
                    </div>
                  </div>

                  {/* 只在当前激活的卡片中显示分页器 */}
                  {index === currentIndex && activePrizes.length > 1 && (
                    <div className={styles.swiperIndicators}>
                      {activePrizes.map((_, dotIndex) => (
                        <div
                          key={dotIndex}
                          className={`${styles.swiperDot} ${dotIndex === currentIndex ? styles.swiperDotActive : ''}`}
                          onClick={() => scrollToIndex(dotIndex)}
                        />
                      ))}
                    </div>
                  )}

                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function TurnTablePreview(props) {
  const { tState, state } = props;
  const main = tState;
  const [activeTab, setActiveTab] = useState(0);
  const [activeProductLine, setActiveProductLine] = useState('');

  // 从state中动态生成所有阶梯数据
  const allSteps = useMemo(() => {
    const seriesList = state?.prizeAndSku?.seriesList;
    if (!seriesList || !Array.isArray(seriesList) || seriesList.length === 0) {
      return [];
    }

    const stepList = seriesList[0]?.stepList;
    if (!stepList || !Array.isArray(stepList)) {
      return [];
    }

    return stepList.map((step, index) => ({
      id: index,
      text: `${step.tankNum || 0}罐可兑`,
      tankNum: step.tankNum || 0,
      // 只显示上架的奖品（status为1或未定义的奖品）
      prizeList: (step.prizeList || []).filter(prize => prize.status === 1 || prize.status === undefined),
    }));
  }, [state?.prizeAndSku?.seriesList]);

  // 生成tabs数据（用于顶部tab切换）
  const tabs = allSteps;

  // 获取所有品线数据
  const productLines = useMemo(() => {
    const spuList = state?.prizeAndSku?.seriesList?.[0]?.seriesSkuList || [];
    const lines = [...new Set(spuList.map((spu: any) => spu.productLine).filter(Boolean))] as string[];
    return lines;
  }, [state?.prizeAndSku?.seriesList]);

  // 根据选中的品线过滤SPU列表
  const filteredSpuList = useMemo(() => {
    const spuList = state?.prizeAndSku?.seriesList?.[0]?.previewSpuList || [];
    if (!activeProductLine) {
      return spuList;
    }
    return spuList.filter(spu => spu.productLine === activeProductLine);
  }, [state?.prizeAndSku?.seriesList, activeProductLine]);


  // 初始化选中第一个品线
  useEffect(() => {
    if (productLines.length > 0 && !activeProductLine) {
      setActiveProductLine(productLines[0]);
    }
  }, [productLines, activeProductLine]);

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
  };

  const handleProductLineClick = (productLine) => {
    setActiveProductLine(productLine);
  };

  return (
    <PhonePreview
      width={290}
    >
      <div style={{ position: 'relative' }}>
        <div
          style={createTurnTableMainStyles(main)}
          className={styles.main}
        >
          <img className={styles.kv} src={main.kv} alt="" />
          <div className={styles.btns}>
            <div>活动规则</div>
            <div>我的订单</div>
            <div>兑换记录</div>
          </div>

          <div className={styles.userInfoBg}>
            <div className={styles.userInfo}>
              <div>XXXXXX用户，您符合转段福利申领资格！</div>
              <div>请点击下方参与活动</div>
            </div>
          </div>

          <img className={styles.step1Bg} src={main.step1Bg} alt="" />
          {/* <div className={styles.step1Bg}> */}
          {/*  <div className={styles.tabContainer}> */}
          {/*    {tabs.map((tab) => ( */}
          {/*      <div */}
          {/*        key={tab.id} */}
          {/*        className={activeTab === tab.id ? styles.getDemoPrizeBtn : styles.step2Bg} */}
          {/*        onClick={() => handleTabClick(tab.id)} */}
          {/*      > */}
          {/*        <div className={styles.stepText}>{tab.text}</div> */}
          {/*      </div> */}
          {/*    ))} */}
          {/*  </div> */}
          {/*  {tabs.length > 1 && ( */}
          {/*    <div className={styles.tabIndicators}> */}
          {/*      {tabs.map((tab) => ( */}
          {/*        <div */}
          {/*          key={tab.id} */}
          {/*          className={`${styles.indicator} ${activeTab === tab.id ? styles.indicatorActive : ''}`} */}
          {/*        /> */}
          {/*      ))} */}
          {/*    </div> */}
          {/*  )} */}
          {/* </div> */}

          {/* 为每个阶梯创建独立的奖品滑动组件 */}
          {allSteps.map((step, stepIndex) => (
            <PrizeSwiper
              key={stepIndex}
              prizes={step.prizeList}
              tankNum={step.tankNum}
              step2Bg={main.step2Bg}
            />
          ))}
          <div className={styles.step3Bg} />
          <div className={styles.skuListLineBox}>
            {productLines.length > 0 && (
              <div className={styles.productLineContainer}>
                {productLines.map((productLine) => (
                  <div
                    key={productLine}
                    className={`${styles.productLineTab} ${activeProductLine === productLine ? styles.active : ''}`}
                    style={{
                      backgroundImage: `url(${activeProductLine === productLine ? main.checkedSkuLine : main.unCheckedSkuLine})`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: '100%',
                    }}
                    onClick={() => handleProductLineClick(productLine)}
                  >
                    <div className={styles.productLineText}>
                      {productLine}
                    </div>
                  </div>
                ))}
              </div>
            )}
            {/* 品线指示器 */}
            {productLines.length > 1 && (
              <div className={styles.productLineIndicators}>
                {productLines.map((productLine) => (
                  <div
                    key={productLine}
                    className={`${styles.productLineIndicator} ${activeProductLine === productLine ? styles.productLineIndicatorActive : ''}`}
                    onClick={() => handleProductLineClick(productLine)}
                  />
                ))}
              </div>
            )}
          </div>
          <div className={styles.skuListBox}>
            {
              filteredSpuList?.map((spu, index) => (
                <div
                  key={index}
                  className={styles.spuItem}
                >
                  <img src={spu.picUrl} alt="" />
                  <div>{spu.name}</div>
                </div>
              ))
            }
          </div>
          <div className={styles.bottomToTop} />
        </div>
      </div>
    </PhonePreview>
  );
}