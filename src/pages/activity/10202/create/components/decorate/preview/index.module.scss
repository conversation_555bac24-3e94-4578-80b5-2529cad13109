.main {
  position: relative;
  background-color: var(--actBgColor);

  .kv {
    width: 100%;
  }

  .btns {
    position: absolute;
    right: -1px;
    top: 140px;

    div {
      background-image: var(--ruleBtn);
      background-repeat: no-repeat;
      background-size: 100%;
      color: var(--ruleBtnColor);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      margin-bottom: 8px;
      padding: 3px 0 0 8px;
      font-size: 10px;
      width: 50px;
      height: 22px;
    }
  }

  .userInfoBg {
    margin: 0 auto;
    width: 100%;
    height: 90px;
    background-image: var(--userInfoBg);
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 20px;

    .userInfo{
      font-size: 11px;
      color: var(--userInfoColor);
      transform: rotate(-3deg);
    }
  }

  .step1Bg {
    //background-image: var(--step1Bg);
    //background-repeat: no-repeat;
    //background-size: 100%;
    width: 100%;
    height: 100%;
    margin: 10px auto 0;
    //padding: 48px 6px 0 6px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .tabContainer {
      // 现在容器宽度为 260 - 12 = 248px，可以完整使用这个宽度
      width: 240px;
      display: flex;
      gap: 10px;
      justify-content: flex-start;
      overflow-x: scroll;
      scroll-behavior: smooth;

      // 隐藏滚动条但保留滚动功能
      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge

      &::-webkit-scrollbar {
        display: none; // Chrome/Safari/Opera
      }
    }

    .step2Bg {
      background-image: var(--step2Bg);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 80px;
      height: 26px;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0; // 防止tab被压缩

      .stepText {
        color: #bc1b2b;
        margin: 0 auto;
        text-align: center;
        line-height: 22px;
        font-size: 12px;
      }
    }

    .getDemoPrizeBtn {
      background-image: var(--getDemoPrizeBtn);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 80px;
      height: 26px;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0; // 防止tab被压缩

      .stepText {
        color: #fdf5bd;
        margin: 0 auto;
        text-align: center;
        line-height: 22px;
        font-size: 12px;
      }
    }
  }

  .tabIndicators {
    display: flex;
    justify-content: center;
    gap: 8px;

    .indicator {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #f0c969;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .indicatorActive {
      background-color: #bc1b2b;
    }
  }

  .step2Bg {
    width: 274px;
    margin: 10px auto 0;
    position: relative;

    .prizeHeader {
      font-size: 14px;
      font-weight: bold;
      color: #bc1b2b;
      text-align: center;
    }

    // Swiper大容器
    .swiperBigBox {
      display: flex;
      justify-content: center;
      height: 200px;
      width: 260px;
      margin: 0 auto;
    }

    .swiperContent {
      padding: 0 1px;
      width: 274px;
      height: 200px;
    }

    .swiperContainer {
      width: 100%;
      height: 100%;
      overflow-x: auto;
      overflow-y: hidden;

      // 隐藏滚动条但保留滚动功能
      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge

      &::-webkit-scrollbar {
        display: none; // Chrome/Safari/Opera
      }
    }

    .swiperWrapper {
      display: flex;
      height: 100%;
    }

    .swiperSlide {
      position: relative;
      padding: 15px 12px 10px;
      height: 200px;
      width: 80px;
      min-width: 180px; // 防止压缩
      transform: scale(0.9);
      transition: transform 0.3s ease;
      flex-shrink: 0;

      .prizeTitle {
        position: absolute;
        transform: translateX(-50%);
        top: 16px;
        left: 50%;
        font-size: 13px;
        font-weight: bold;
        color: #f5e3c7;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        font-style: italic;
      }

      .prizeGoods {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        height: 100px;
        width: 200px;
        margin: 30px auto 0;
        padding: 9px 7px;

        .prizeGoodsImgBox {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 6px;
          width: 88px;
          height: 88px;
          border-radius: 8px;
          margin-right: 8px;

          .prizeGoodsImg {
            width: 70px;
            height: 70px;
            object-fit: cover;
          }
        }

        .prizeGoodsInfo {
          flex: 1;
          height: 88px;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .prizeGoodsName {
            margin-bottom: 4px;
            font-size: 10px;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.2;
          }

          .step2ItemBg {
            background-image: var(--step2ItemBg);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: white;
            font-size: 10px;
            padding: 6px 20px;
            margin: 2px 0;
          }

          .prizeGoodsRemain {
            font-size: 8px;
            color: #ff0000;
            white-space: nowrap;
          }
        }
      }
    }

    // 当前激活的滑块
    .swiperSlideActive {
      transform: scale(1);
    }

    // 滑动指示器
    .swiperIndicators {
      display: flex;
      justify-content: center;
      gap: 6px;
      margin-top: 10px;

      .swiperDot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: rgba(188, 27, 43, 0.3);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background-color: rgba(188, 27, 43, 0.6);
        }
      }

      .swiperDotActive {
        background-color: #bc1b2b;
      }
    }

    .noPrize {
      text-align: center;
      color: #999;
      font-size: 12px;
      padding: 40px 0;
    }
  }

  .step3Bg {
    background-image: var(--step3Bg);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 260px;
    height: 90px;
    margin: 0 auto;
  }

  .skuListLineBox {
    margin: 0 auto;
    padding: 0 10px;
    overflow: hidden;

    .productLineContainer {
      display: flex;
      gap: 10px;
      justify-content: flex-start;
      overflow-x: auto;
      scroll-behavior: smooth;
      padding: 5px 0;

      // 隐藏滚动条但保留滚动功能
      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge

      &::-webkit-scrollbar {
        display: none; // Chrome/Safari/Opera
      }

      .productLineTab {
        height: 22px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px;
        margin-right: 5px;



        .productLineText {
          font-size: 14px;
          font-weight: 500;
          text-align: center;
          line-height: 1.2;
          color: #fdf5bd; // 未选中
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          z-index: 1;
          position: relative;
        }

        // 选中状态的文字颜色
        &.active .productLineText {
          color: #bc1b2b; // 选中
        }
      }
    }

    // 品线指示器
    .productLineIndicators {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;
      margin-bottom: 5px;

      .productLineIndicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #f0c969;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
        }
      }

      .productLineIndicatorActive {
        background-color: #fff;
      }
    }
  }

  .skuListBox {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px 15px;
    justify-content: flex-start;
    max-width: 260px;
    margin: 0 auto;

    .spuItem {
      background-image: var(--ruleImage);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: calc(50% - 5px);
      max-width: 115px;
      height: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0 10px 10px;
      border-radius: 8px;
      cursor: pointer;


      img {
        width: 70px;
        height: 70px;
        object-fit: cover;
        border-radius: 4px;
        margin-bottom: 2px;
      }

      div {
        font-size: 12px;
        color: #333;
        text-align: center;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }

  .bottomToTop {
    background-image: var(--bottomToTop);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 280px;
    height: 44px;
    margin: 0 auto;
  }
}

// 分页器样式
.swiperIndicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  padding: 10px 0;
}

.swiperDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(188, 27, 43, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(188, 27, 43, 0.6);
    transform: scale(1.1);
  }
}

.swiperDotActive {
  background-color: #bc1b2b;
}

.noPrize {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 40px 0;
}

// 卡片内分页器样式
.swiperIndicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  border-radius: 10px;
  margin: 0 auto;
  backdrop-filter: blur(2px);
}

.swiperDot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
  }
}

.swiperDotActive {
  background-color: #ffffff;
  transform: scale(1.4);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}